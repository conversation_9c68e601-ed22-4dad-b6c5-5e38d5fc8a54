/** @type {import('tailwindcss').Config} */
export default {
  content: ["./index.html", "./src/**/*.{js,ts,jsx,tsx}"],
  theme: {
    extend: {
      animation: {
        fadeIn: "fadeIn 0.6s ease-out",
        floatBubble: "floatBubble 25s infinite ease-in-out",
        "floatBubble-reverse": "floatBubble 30s infinite ease-in-out reverse",
        kenBurns: "kenBurns 10s ease-in-out infinite alternate",
      },
      fontFamily: {
        serif: ["DM Serif Display", "serif"],
      },
    },
  },
  plugins: [],
  // Optimize for production
  future: {
    removeDeprecatedGapUtilities: true,
    purgeLayersByDefault: true,
  },
};

