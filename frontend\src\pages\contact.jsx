import { useState, useEffect } from 'react';
import emailjs from '@emailjs/browser';
import SEO from '../components/common/SEO';

// Lazy load the emailjs library
const initEmailJs = () => {
  emailjs.init("EpwUY5CAqo4O5Hso3");
};

const Contact = () => {
  // Initialize emailjs when component mounts
  useEffect(() => {
    initEmailJs();
  }, []);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone_number: '+91  ', // Initialize with default country code
    destination: '',
    message: ''
  });

  const [status, setStatus] = useState('');

  const destinations = [
    'Select Destination',
    'Darjeeling',
    'Darjeeling-Gangtok',
    'Gangtok-North Sikkim (Lachen, Lachung)',
    'Darjeeling-Gangtok-North Sikkim (Lachen, Lachung)',
  ];

  // Removed direct initialization - now handled in useEffect

  const handleChange = (e) => {
    const { name, value } = e.target;

    if (name === "phone_number") {
      // Allow only '+' and digits and spaces while typing
      if (/^[+\d\s]*$/.test(value) && value.length <= 16) {
        setFormData({ ...formData, [name]: value });
      }
    } else {
      setFormData({ ...formData, [name]: value });
    }
  };


  const handleSubmit = async (e) => {
    e.preventDefault();
    setStatus('sending');

    const templateParams = {
      to_name: "Trypindia",
      from_name: formData.name,
      from_email: formData.email,
      phone_number: formData.phone_number,
      destination: formData.destination,
      message: formData.message,
      reply_to: formData.email,
    };

    try {
      const result = await emailjs.send(
        'service_hg60f9q',
        'template_0ofn4jc',
        templateParams
      );

      console.log('Email sent successfully:', result.text);
      setStatus('sent');

      setFormData({
        name: '',
        email: '',
        phone_number: '+91 ',
        destination: '',
        message: ''
      });

      alert('Thank you for your message! We will get back to you soon.');

    } catch (error) {
      console.error('Failed to send email:', error);
      setStatus('error');
      alert('Failed to send message. Please try again or contact us directly.');
    }
  };

  return (
    <div className="min-h-[calc(100vh-80px)] py-20 px-4 md:px-8 lg:px-12 bg-white dark:bg-gray-900 relative overflow-hidden">
      <SEO
        title="Contact Us - TRYPINDIA Travel"
        description="Get in touch with our team to plan your perfect trip to North East India. We'll respond within an hour!"
        keywords="contact, travel inquiry, north east india travel, darjeeling trip, gangtok vacation"
      />

      {/* Main Content */}
      <div className="max-w-6xl mx-auto">
        <h1 className="text-center font-serif text-3xl md:text-4xl font-bold mb-6 mt-10 text-black">Get in Touch With Us</h1>
        <p className="text-center font-serif mb-10 max-w-2xl mx-auto text-gray-600">We're here to help plan your perfect trip to North East India. Reach out to us and we'll respond within an hour.</p>

        <div className="w-full flex flex-col md:flex-row gap-8 mx-auto bg-white rounded-2xl shadow-lg overflow-hidden relative z-10 animate-fadeIn">
          {/* Left section */}
          <div className="flex-1 p-8 md:p-12 bg-gradient-to-br from-blue-400 to-blue-500 text-white relative overflow-hidden">
            <h2 className="text-2xl md:text-3xl font-bold mb-6 animate-fadeIn font-serif text-center">
              Contact Information
            </h2>
            <p className="text-indigo-100 leading-relaxed mb-10 animate-fadeIn font-serif">
              We'll get back to you within 1 hour to discuss your preferences and plan a perfect trip together!
            </p>

            <div className="flex flex-col gap-6">
              <div className="flex items-center gap-4 p-4 bg-white/10 backdrop-blur-sm rounded-xl transition-all duration-300 hover:bg-white/15 animate-fadeIn">
                <div className="w-12 h-12 rounded-full bg-white/20 flex items-center justify-center text-xl">
                  <i className="fas fa-phone"></i>
                </div>
                <div>
                  <h3 className="font-medium font-serif text-white">Call Us</h3>
                  <p className="text-indigo-100">+91 8584807189</p>
                </div>
              </div>

              <div className="flex items-center gap-4 p-4 bg-white/10 backdrop-blur-sm rounded-xl transition-all duration-300 hover:bg-white/15 animate-fadeIn">
                <div className="w-12 h-12 rounded-full bg-white/20 flex items-center justify-center text-xl">
                  <i className="fas fa-envelope"></i>
                </div>
                <div>
                  <h3 className="font-medium font-serif text-white">Email Us</h3>
                  <p className="text-indigo-100 font-serif"><EMAIL></p>
                </div>
              </div>

              <div className="flex items-center gap-4 p-4 bg-white/10 backdrop-blur-sm rounded-xl transition-all duration-300 hover:bg-white/15 animate-fadeIn">
                <div className="w-12 h-12 rounded-full bg-white/20 flex items-center justify-center text-xl">
                  <i className="fas fa-map-marker-alt"></i>
                </div>
                <div>
                  <h3 className="font-medium font-serif">Our Destinations</h3>
                  <p className="text-indigo-100 font-serif">Darjeeling, Gangtok, North Sikkim</p>
                </div>
              </div>
            </div>

            {/* Decorative elements */}
            <div className="absolute bottom-0 right-0 w-32 h-32 bg-white/100 rounded-full -mb-16 -mr-16"></div>
            <div className="absolute top-0 right-0 w-20 h-20 bg-white/100 rounded-full -mt-10 -mr-10"></div>
          </div>

          {/* Right section */}
          <div className="flex-1 p-8 md:p-12 animate-fadeIn">
            <div className="w-full">
              <h2 className="text-2xl md:text-3xl font-bold mb-6 text-gray-800 font-serif text-center">
                Send Message
              </h2>
              <form onSubmit={handleSubmit} className="w-full">
                <div className="mb-5">
                  <label htmlFor="name" className="hidden">Name</label>
                  <input
                    id="name"
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    required
                    placeholder="Your Name"
                    className="w-full p-3 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg text-gray-800 dark:text-white transition-all duration-300 focus:border-indigo-500 focus:ring-2 focus:ring-indigo-200 dark:focus:ring-indigo-800 focus:outline-none"
                  />
                </div>

                <div className="mb-5">
                  <label htmlFor="email" className="hidden">Email</label>
                  <input
                    id="email"
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    required
                    placeholder="Your Email"
                    className="w-full p-3 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg text-gray-800 dark:text-white transition-all duration-300 focus:border-indigo-500 focus:ring-2 focus:ring-indigo-200 dark:focus:ring-indigo-800 focus:outline-none"
                  />
                </div>

                <div className="mb-5">
                  <label htmlFor="phone_number" className="hidden">Phone Number</label>
                  <input
                    id="phone_number"
                    type="tel"
                    name="phone_number"
                    value={formData.phone_number}
                    onChange={handleChange}
                    required
                    placeholder="+91 1234567890"
                    pattern="^\+\d{1,3}\s?\d{10}$"
                    title="Please enter country code (e.g. +91) followed by your 10-digit phone number"
                    className="w-full p-3 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg text-gray-800 dark:text-white transition-all duration-300 focus:border-indigo-500 focus:ring-2 focus:ring-indigo-200 dark:focus:ring-indigo-800 focus:outline-none tracking-wider"
                  />
                </div>

                <div className="mb-5">
                  <label htmlFor="destination" className="hidden">Destination</label>
                  <select
                    id="destination"
                    name="destination"
                    value={formData.destination}
                    onChange={handleChange}
                    required
                    className="w-full p-3 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg text-gray-800 dark:text-white transition-all duration-300 focus:border-indigo-500 focus:ring-2 focus:ring-indigo-200 dark:focus:ring-indigo-800 focus:outline-none appearance-none bg-no-repeat bg-[url('data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20fill%3D%22none%22%20viewBox%3D%220%200%2020%2020%22%3E%3Cpath%20stroke%3D%22%236b7280%22%20stroke-linecap%3D%22round%22%20stroke-linejoin%3D%22round%22%20stroke-width%3D%221.5%22%20d%3D%22M6%208l4%204%204-4%22%2F%3E%3C%2Fsvg%3E')] bg-[right_0.5rem_center] bg-[length:1.5em]"
                  >
                    {destinations.map((dest, index) => (
                      <option key={index} value={index === 0 ? "" : dest} className="bg-white dark:bg-gray-700 text-gray-800 dark:text-white">
                        {dest}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="mb-5">
                  <label htmlFor="message" className="hidden">Message</label>
                  <textarea
                    id="message"
                    name="message"
                    value={formData.message}
                    onChange={handleChange}
                    required
                    placeholder="Your Message"
                    rows="4"
                    className="w-full p-3 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg text-gray-800 dark:text-white transition-all duration-300 focus:border-indigo-500 focus:ring-2 focus:ring-indigo-200 dark:focus:ring-indigo-800 focus:outline-none"
                  ></textarea>
                </div>

                <button
                  type="submit"
                  className={`w-full py-3 px-6 bg-blue-400 text-white rounded-lg text-lg font-medium transition-all duration-300 font-serif ${
                    status === "sending" ? "opacity-75 cursor-not-allowed" : ""
                  } hover:shadow-lg hover:shadow-indigo-200 active:scale-[0.98]`}
                  disabled={status === "sending"}
                >
                  {status === "sending"
                    ? "Sending..."
                    : status === "sent"
                    ? "Message Sent!"
                    : status === "error"
                    ? "Error Sending"
                    : "Send Message"}
                </button>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Contact;