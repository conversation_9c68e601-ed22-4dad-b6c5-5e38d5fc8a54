import { useParams, useNavigate } from 'react-router-dom';
import { useEffect } from 'react';
import { useAuthStore } from '../../store/useAuthStore';
import { useWishlistStore } from '../../store/useWishlistStore';
import toast from 'react-hot-toast';
import { packageDetails } from '../../data/packageDetails';

const PackageDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { user } = useAuthStore();
  const { addToWishlist, removeFromWishlist, isInWishlist, initializeWishlist } = useWishlistStore();

  // Initialize wishlist when component mounts
  useEffect(() => {
    if (user) {
      initializeWishlist(user._id);
    }
  }, [user, initializeWishlist]);

  // Package data matching Plan.jsx
  
  const packageData = packageDetails.find(pkg => pkg.id === parseInt(id));

  if (!packageData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-800 mb-4">Package Not Found</h2>
          <button
            onClick={() => navigate('/plan')}
            className="bg-blue-400 text-white px-6 py-3 rounded-lg hover:bg-blue-500 transition-colors"
          >
            Back to Packages
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 pt-24">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Back Button */}
        <button
          onClick={() => navigate("/plan")}
          className="flex items-center text-gray-600 hover:text-gray-800 mb-6 transition-colors"
        >
          <i className="fas fa-arrow-left mr-2"></i>
          Back to Packages
        </button>

        {/* Package Header */}
        <div className="bg-white rounded-xl shadow-lg overflow-hidden mb-8">
          <div className="p-6 border-b border-gray-200">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-900 mb-2">
                  {packageData.location}
                </h1>
                <div className="flex items-center text-gray-600 space-x-4">
                  <span className="flex items-center">
                    <i className="fas fa-clock mr-2"></i>
                    {packageData.duration}
                  </span>
                  <span className="flex items-center">
                    <i className="fas fa-users mr-2"></i>
                    {packageData.no_of_persons} Persons
                  </span>
                </div>
              </div>
              <div className="mt-4 md:mt-0">
                <div className="text-3xl font-bold text-blue-600">
                  {packageData.price}
                </div>
                <div className="text-sm text-gray-500">per person</div>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left Side - Package Image */}
          <div className="bg-white rounded-xl shadow-lg overflow-hidden">
            <div className="p-6">
              <h2 className="text-xl font-bold text-gray-900 mb-4">
                Package Overview
              </h2>
              <div className="aspect-w-16 aspect-h-12 mb-6">
                <img
                  src={packageData.image}
                  alt={packageData.location}
                  className="w-full h-80 object-cover rounded-lg"
                />
              </div>
              {/* Book Now & Contact Us Buttons */}
              <div className="mt-8 bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <button
                    onClick={() => navigate("/contact")}
                    className="bg-blue-400 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-500 transition-colors flex items-center justify-center"
                  >
                    <i className="fas fa-envelope mr-2"></i>
                    Book Now
                  </button>
                  <button
                    onClick={() => {
                      if (!user) {
                        toast.error('Please login to add items to wishlist');
                        navigate('/login');
                        return;
                      }

                      const packageId = parseInt(id);
                      if (isInWishlist(packageId)) {
                        removeFromWishlist(packageId, user._id);
                      } else {
                        addToWishlist(packageId, user._id);
                      }
                    }}
                    className={`px-8 py-3 rounded-lg font-semibold transition-colors flex items-center justify-center ${
                      user && isInWishlist(parseInt(id))
                        ? 'bg-red-500 text-white hover:bg-red-600'
                        : 'bg-white dark:bg-gray-700 border border-blue-500 text-blue-500 dark:text-blue-400 hover:bg-indigo-50 dark:hover:bg-gray-600'
                    }`}
                  >
                    <i className={`fas fa-heart mr-2 ${user && isInWishlist(parseInt(id)) ? 'text-white' : ''}`}></i>
                    {user && isInWishlist(parseInt(id)) ? 'Remove from Wishlist' : 'Add to Wishlist'}
                  </button>
                </div>
              </div>

              {/* Included Items */}
              <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  Included
                </h3>
                <div className="grid grid-cols-2 gap-4">
                  {packageData.included.map((item, index) => (
                    <div
                      key={index}
                      className="flex items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
                    >
                      <div className="w-10 h-10 bg-indigo-100 dark:bg-indigo-900 rounded-full flex items-center justify-center mr-3">
                        <i className={`fas ${item.icon} text-blue-500 dark:text-blue-400`}></i>
                      </div>
                      <span className="text-gray-700 dark:text-gray-300 font-medium">
                        {item.text}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Right Side - Itinerary Details */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden">
            <div className="p-6">
              <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-6">
                Itinerary Details
              </h2>

              <div className="space-y-6">
                {packageData.itinerary.map((day, index) => (
                  <div
                    key={index}
                    className="border-l-4 border-blue-500 pl-6 relative"
                  >
                    {/* Day Number Circle */}
                    <div className="absolute -left-3 top-0 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                      <span className="text-white text-sm font-bold">
                        {day.day}
                      </span>
                    </div>

                    <div className="pb-6">
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                        Day {day.day} - {day.title}
                      </h3>
                      <ul className="space-y-2">
                        {day.activities.map((activity, actIndex) => (
                          <li key={actIndex} className="flex items-start">
                            <div className="w-2 h-2 bg-gray-400 dark:bg-gray-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                            <span className="text-gray-700 dark:text-gray-300">{activity}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
      </div>
    </div>
  );
};

export default PackageDetails;